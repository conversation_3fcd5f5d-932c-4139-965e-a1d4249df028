import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import '../services/post_service.dart';
import '../models/poll_model.dart';

/// GetX Controller for managing posts state across the entire app
class PostController extends GetxController {
  static PostController get instance => Get.find();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final PostService _postService = PostService();

  // Reactive variables
  final _posts = <Post>[].obs;
  final _isLoading = false.obs;
  final _isInitialized = false.obs;
  final _selectedCategory = 'All'.obs;
  final _sortBy = 'Highest Paid'.obs;
  final _topPostCache = <String, Post?>{}.obs;
  final _lastTopPostUpdate = <String, DateTime>{}.obs;

  // Stream subscriptions
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? _postsSubscription;
  StreamController<List<Post>>? _postsController;

  // Real-time configuration - No memory limits, Firebase function handles cleanup

  // Cache duration for top posts (5 minutes)
  static const Duration _topPostCacheDuration = Duration(minutes: 5);

  // Getters
  List<Post> get posts => _posts;
  bool get isLoading => _isLoading.value;
  bool get isInitialized => _isInitialized.value;
  String get selectedCategory => _selectedCategory.value;
  String get sortBy => _sortBy.value;

  // Reactive getters for UI binding
  RxList<Post> get postsRx => _posts;
  RxBool get isLoadingRx => _isLoading;
  RxBool get isInitializedRx => _isInitialized;
  RxString get selectedCategoryRx => _selectedCategory;
  RxString get sortByRx => _sortBy;

  @override
  void onInit() {
    super.onInit();
    _initializeController();
  }

  @override
  void onClose() {
    _postsSubscription?.cancel();
    _postsController?.close();
    super.onClose();
  }

  /// Initialize the controller
  Future<void> _initializeController() async {
    if (_isInitialized.value) return;

    try {
      _isLoading.value = true;

      // Don't initialize PostService - we'll handle everything here
      // Set up our own real-time listener
      await _setupRealtimeListener();

      _isInitialized.value = true;
      debugPrint('PostController: Initialized successfully');
    } catch (e) {
      debugPrint('PostController: Error initializing: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Set up comprehensive real-time listener for posts (NO PAGINATION LIMITS)
  Future<void> _setupRealtimeListener() async {
    _postsSubscription?.cancel();

    // Listen to ALL posts for true real-time functionality
    _postsSubscription = _firestore
        .collection('posts')
        .orderBy('createdAt', descending: true)
        // NO LIMIT - Listen to all posts for comprehensive real-time updates
        .snapshots()
        .listen(
      (snapshot) {
        _handleRealtimePostUpdate(snapshot);
        _invalidateTopPostCache();
        debugPrint('PostController: Real-time update - ${_posts.length} posts');
      },
      onError: (error) {
        debugPrint('PostController: Error in real-time listener: $error');
      },
    );
  }

  /// Handle real-time post updates with smart memory management
  void _handleRealtimePostUpdate(QuerySnapshot<Map<String, dynamic>> snapshot) {
    final newPosts = snapshot.docs.map((doc) {
      final data = doc.data();
      final map = Map<String, dynamic>.from(data)..['id'] = doc.id;
      return Post.fromMap(map);
    }).toList();

    // Detect truly new posts
    final existingIds = _posts.map((p) => p.id).toSet();
    final trulyNewPosts =
        newPosts.where((p) => !existingIds.contains(p.id)).toList();

    if (trulyNewPosts.isNotEmpty) {
      debugPrint('PostController: ${trulyNewPosts.length} new posts detected');

      // Insert new posts at the beginning for real-time experience
      _insertNewPostsAtTop(trulyNewPosts);
    }

    // Update existing posts with new data (likes, views, etc.)
    _updateExistingPostsData(newPosts);

    // Sort posts based on current sort criteria
    _applySorting();

    // Invalidate cache
    _invalidateTopPostCache();

    // Notify specific GetBuilder listeners for each category
    final categories = getCategories();
    for (final category in categories) {
      update(['posts_$category']);
    }

    debugPrint(
        'PostController: Real-time update complete - ${_posts.length} posts');
  }

  /// Insert new posts and maintain proper sorting
  void _insertNewPostsAtTop(List<Post> newPosts) {
    // Add new posts to the list
    _posts.addAll(newPosts);

    // Re-sort the entire list to maintain proper order based on current sort criteria
    _applySorting();

    debugPrint(
        'PostController: Added ${newPosts.length} new posts and re-sorted by ${_sortBy.value}');
  }

  /// Update existing posts with new data
  void _updateExistingPostsData(List<Post> allPosts) {
    final postMap = {for (var post in allPosts) post.id: post};

    for (int i = 0; i < _posts.length; i++) {
      final existingPost = _posts[i];
      final updatedPost = postMap[existingPost.id];

      if (updatedPost != null &&
          _hasPostDataChanged(existingPost, updatedPost)) {
        _posts[i] = updatedPost;
      }
    }
  }

  /// Check if post data has changed
  bool _hasPostDataChanged(Post oldPost, Post newPost) {
    return oldPost.likes != newPost.likes ||
        oldPost.views != newPost.views ||
        oldPost.isPaid != newPost.isPaid ||
        oldPost.likedBy.length != newPost.likedBy.length;
  }

  /// Apply sorting to posts based on current sort criteria
  void _applySorting() {
    switch (_sortBy.value) {
      case 'Highest Paid':
        _posts.sort((a, b) {
          final priceComparison = b.price.compareTo(a.price);
          if (priceComparison != 0) return priceComparison;
          return b.createdAt.compareTo(a.createdAt);
        });
        break;
      case 'Most Popular':
        _posts.sort((a, b) {
          final likesComparison = b.likes.compareTo(a.likes);
          if (likesComparison != 0) return likesComparison;
          return b.createdAt.compareTo(a.createdAt);
        });
        break;
      case 'Recent':
        _posts.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'Most Views':
        _posts.sort((a, b) {
          final viewsComparison = b.views.compareTo(a.views);
          if (viewsComparison != 0) return viewsComparison;
          return b.createdAt.compareTo(a.createdAt);
        });
        break;
    }
  }

  /// Get filtered and sorted posts
  List<Post> getFilteredPosts({String? category}) {
    final categoryFilter = category ?? _selectedCategory.value;

    if (categoryFilter == 'All') {
      return List<Post>.from(_posts);
    }

    return _posts.where((post) => post.category == categoryFilter).toList();
  }

  /// Get filtered posts as a reactive stream
  Stream<List<Post>> getFilteredPostsStream({String? category}) {
    return _posts.stream.map((posts) {
      final categoryFilter = category ?? _selectedCategory.value;

      if (categoryFilter == 'All') {
        return List<Post>.from(posts);
      }

      return posts.where((post) => post.category == categoryFilter).toList();
    });
  }

  /// Update category filter
  void updateCategory(String category) {
    if (_selectedCategory.value != category) {
      _selectedCategory.value = category;
      // Invalidate top post cache when category changes
      _invalidateTopPostCache();
      // Trigger UI update
      update();
      debugPrint(
          'PostController: Updated category to $category and invalidated cache');
    }
  }

  /// Update sort criteria
  void updateSortBy(String sortBy) {
    if (_sortBy.value != sortBy) {
      _sortBy.value = sortBy;
      _applySorting();
      update();
      debugPrint('PostController: Updated sort to $sortBy');
    }
  }

  /// Get top paid post for a category - SIMPLIFIED: Just the first post from sorted list
  Post? getTopPaidPostForCategory(String category) {
    final now = DateTime.now();
    final cacheKey = category;

    // Check if we have a valid cached result
    if (_topPostCache.containsKey(cacheKey) &&
        _lastTopPostUpdate.containsKey(cacheKey)) {
      final lastUpdate = _lastTopPostUpdate[cacheKey]!;
      if (now.difference(lastUpdate) < _topPostCacheDuration) {
        return _topPostCache[cacheKey];
      }
    }

    // Get filtered and sorted posts (using the same logic as main feed)
    final filteredPosts = getFilteredPosts(category: category);

    if (filteredPosts.isEmpty) {
      _topPostCache[cacheKey] = null;
      _lastTopPostUpdate[cacheKey] = now;
      return null;
    }

    // Apply the same sorting as the main feed based on current sort criteria
    final sortedPosts = List<Post>.from(filteredPosts);
    switch (_sortBy.value) {
      case 'Highest Paid':
        sortedPosts.sort((a, b) {
          final priceComparison = b.price.compareTo(a.price);
          if (priceComparison != 0) return priceComparison;
          return b.createdAt.compareTo(a.createdAt);
        });
        break;
      case 'Most Popular':
        sortedPosts.sort((a, b) {
          final likesComparison = b.likes.compareTo(a.likes);
          if (likesComparison != 0) return likesComparison;
          return b.createdAt.compareTo(a.createdAt);
        });
        break;
      case 'Recent':
        sortedPosts.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case 'Most Views':
        sortedPosts.sort((a, b) {
          final viewsComparison = b.views.compareTo(a.views);
          if (viewsComparison != 0) return viewsComparison;
          return b.createdAt.compareTo(a.createdAt);
        });
        break;
    }

    // Top post is simply the first post from the sorted list
    final topPost = sortedPosts.first;

    // Cache the result
    _topPostCache[cacheKey] = topPost;
    _lastTopPostUpdate[cacheKey] = now;

    debugPrint(
      'PostController: Top post for "$category": ${topPost.id} (Price: \$${topPost.price}, Sort: ${_sortBy.value})',
    );

    return topPost;
  }

  /// Invalidate top post cache
  void _invalidateTopPostCache() {
    _topPostCache.clear();
    _lastTopPostUpdate.clear();
  }

  /// Force refresh posts
  Future<void> refreshPosts() async {
    try {
      _isLoading.value = true;

      // Invalidate cache
      _invalidateTopPostCache();

      // Restart listener
      await _setupRealtimeListener();

      // Notify all category-specific listeners to clear their cache
      final categories = getCategories();
      for (final category in categories) {
        update(['posts_$category']);
      }

      debugPrint('PostController: Posts refreshed');
    } catch (e) {
      debugPrint('PostController: Error refreshing posts: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Create a new post
  Future<String> createPost({
    String? title,
    required String content,
    required double price,
    required String category,
    List<String>? tags,
    bool isPublic = true,
    bool allowComments = true,
    String? imageUrl,
    List<String>? imageUrls,
    List<String>? videoUrls,
    String? linkUrl,
    Poll? poll,
  }) async {
    final postId = await _postService.createPost(
      title: title,
      content: content,
      price: price,
      category: category,
      tags: tags,
      isPublic: isPublic,
      allowComments: allowComments,
      imageUrl: imageUrl,
      imageUrls: imageUrls,
      videoUrls: videoUrls,
      linkUrl: linkUrl,
      poll: poll,
    );

    // Invalidate cache since we have a new post
    _invalidateTopPostCache();

    return postId;
  }

  /// Get categories
  List<String> getCategories() {
    return _postService.getCategories();
  }

  /// Load more historical posts for pagination (hybrid approach)
  Future<bool> loadMoreHistoricalPosts({String? category}) async {
    try {
      final historicalPosts = await _postService.loadHistoricalPosts(
        category: category,
        limit: 20,
      );

      if (historicalPosts.isNotEmpty) {
        // Add historical posts to the end of current posts
        final existingIds = _posts.map((p) => p.id).toSet();
        final uniqueHistoricalPosts =
            historicalPosts.where((p) => !existingIds.contains(p.id)).toList();

        if (uniqueHistoricalPosts.isNotEmpty) {
          _posts.addAll(uniqueHistoricalPosts);

          // Notify UI
          final categories = getCategories();
          for (final cat in categories) {
            update(['posts_$cat']);
          }

          debugPrint(
              'PostController: Added ${uniqueHistoricalPosts.length} historical posts');
          return true;
        }
      }

      return false;
    } catch (e) {
      debugPrint('PostController: Error loading historical posts: $e');
      return false;
    }
  }

  /// Get real-time posts stream for a category
  Stream<List<Post>> getRealtimePostsStream({String? category}) {
    return _postService.getRealtimePostsWithPagination(category: category);
  }

  /// Get sort options
  List<String> getSortOptions() {
    return _postService.getSortOptions();
  }

  /// Vote on a poll
  Future<void> voteOnPoll(String postId, String optionId) async {
    try {
      await _postService.voteOnPoll(postId, optionId);
      // Trigger UI update after voting
      update(['posts_All', 'posts_${_selectedCategory.value}', 'poll_$postId']);
      debugPrint('Poll vote UI updated for post: $postId');
    } catch (e) {
      debugPrint('Error voting on poll: $e');
    }
  }
}
