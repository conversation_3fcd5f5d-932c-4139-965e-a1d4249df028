import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/poll_model.dart';

class PollWidget extends StatelessWidget {
  final Poll poll;
  final Function(String optionId)? onVote;
  final bool isDetailView;

  const PollWidget({
    super.key,
    required this.poll,
    this.onVote,
    this.isDetailView = false,
  });

  @override
  Widget build(BuildContext context) {
    final currentUserId = FirebaseAuth.instance.currentUser?.uid ?? '';
    final hasUserVoted = poll.hasUserVoted(currentUserId);
    final userVotedOption = poll.getUserVotedOption(currentUserId);
    final canVote = poll.canVote && !hasUserVoted && onVote != null;
    final isVotingDisabled = onVote == null;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isVotingDisabled ? Colors.grey[100] : Colors.blue[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isVotingDisabled ? Colors.grey[300]! : Colors.blue[200]!,
          width: isVotingDisabled ? 1 : 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Poll question
          Row(
            children: [
              Icon(
                Icons.poll,
                size: 18,
                color: Colors.blue[600],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  poll.question,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Poll options
          ...poll.options.map((option) => _buildPollOption(
                context,
                option,
                hasUserVoted,
                userVotedOption?.id == option.id,
                canVote,
                isVotingDisabled,
              )),

          const SizedBox(height: 8),

          // Poll metadata
          Row(
            children: [
              Text(
                '${poll.totalVotes} vote${poll.totalVotes != 1 ? 's' : ''}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (poll.expiresAt != null) ...[
                const SizedBox(width: 8),
                Text(
                  '•',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  poll.hasExpired ? 'Expired' : _getTimeRemaining(),
                  style: TextStyle(
                    fontSize: 12,
                    color: poll.hasExpired ? Colors.red[600] : Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
              if (isVotingDisabled) ...[
                const SizedBox(width: 8),
                Text(
                  '•',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Purchase to vote',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.orange[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPollOption(
    BuildContext context,
    PollOption option,
    bool hasUserVoted,
    bool isUserChoice,
    bool canVote,
    bool isVotingDisabled,
  ) {
    final percentage = option.getPercentage(poll.totalVotes);
    final isLeading = poll.totalVotes > 0 &&
        poll.options.isNotEmpty &&
        option.votes ==
            poll.options.map((o) => o.votes).reduce((a, b) => a > b ? a : b);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: canVote ? () => onVote!(option.id) : null,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isUserChoice
                    ? Colors.blue[400]!
                    : (canVote ? Colors.grey[300]! : Colors.grey[200]!),
                width: isUserChoice ? 2 : 1,
              ),
              color: hasUserVoted
                  ? (isUserChoice ? Colors.blue[50] : Colors.grey[50])
                  : (canVote
                      ? Colors.white
                      : (isVotingDisabled
                          ? Colors.grey[100]
                          : Colors.grey[50])),
            ),
            child: Stack(
              children: [
                // Progress bar background (only show if votes exist)
                if (hasUserVoted && poll.totalVotes > 0)
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: FractionallySizedBox(
                        alignment: Alignment.centerLeft,
                        widthFactor: percentage / 100,
                        child: Container(
                          decoration: BoxDecoration(
                            color: isUserChoice
                                ? Colors.blue[200]!.withOpacity(0.3)
                                : Colors.grey[300]!.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(6),
                          ),
                        ),
                      ),
                    ),
                  ),

                // Option content
                Row(
                  children: [
                    // Option text
                    Expanded(
                      child: Text(
                        option.text,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight:
                              isUserChoice ? FontWeight.w600 : FontWeight.w500,
                          color: canVote ? Colors.black87 : Colors.black54,
                        ),
                      ),
                    ),

                    // Vote count and percentage (show after voting)
                    if (hasUserVoted) ...[
                      const SizedBox(width: 8),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (isLeading && poll.totalVotes > 0)
                            Icon(
                              Icons.trending_up,
                              size: 14,
                              color: Colors.green[600],
                            ),
                          if (isUserChoice)
                            Icon(
                              Icons.check_circle,
                              size: 14,
                              color: Colors.blue[600],
                            ),
                          const SizedBox(width: 4),
                          Text(
                            '${percentage.toStringAsFixed(0)}%',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: isUserChoice
                                  ? Colors.blue[700]
                                  : Colors.grey[700],
                            ),
                          ),
                        ],
                      ),
                    ],

                    // Vote button indicator
                    if (canVote) ...[
                      const SizedBox(width: 8),
                      Icon(
                        Icons.radio_button_unchecked,
                        size: 16,
                        color: Colors.grey[400],
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getTimeRemaining() {
    if (poll.expiresAt == null) return '';

    final now = DateTime.now();
    final difference = poll.expiresAt!.difference(now);

    if (difference.isNegative) return 'Expired';

    if (difference.inDays > 0) {
      return '${difference.inDays}d left';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h left';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m left';
    } else {
      return 'Ending soon';
    }
  }
}
