import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/poll_model.dart';

class PollWidget extends StatefulWidget {
  final Poll poll;
  final Function(String optionId)? onVote;
  final bool isDetailView;

  const PollWidget({
    super.key,
    required this.poll,
    this.onVote,
    this.isDetailView = false,
  });

  @override
  State<PollWidget> createState() => _PollWidgetState();
}

class _PollWidgetState extends State<PollWidget> {
  late Poll _currentPoll;
  bool _isVoting = false;

  @override
  void initState() {
    super.initState();
    _currentPoll = widget.poll;
  }

  @override
  void didUpdateWidget(PollWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.poll != widget.poll) {
      setState(() {
        _currentPoll = widget.poll;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentUserId = FirebaseAuth.instance.currentUser?.uid ?? '';
    final hasUserVoted = _currentPoll.hasUserVoted(currentUserId);
    final userVotedOption = _currentPoll.getUserVotedOption(currentUserId);
    // Allow voting even if user has voted (to change vote) and if poll is active
    final canVote = _currentPoll.canVote && widget.onVote != null && !_isVoting;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6),
      padding: const EdgeInsets.all(14),
      decoration: BoxDecoration(
        color: _isVoting ? Colors.blue[100] : Colors.blue[25],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _isVoting ? Colors.blue[400]! : Colors.blue[200]!,
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Poll question
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.blue[100],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Icon(
                  Icons.poll,
                  size: 14,
                  color: Colors.blue[700],
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _currentPoll.question,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Poll options
          ..._currentPoll.options.map((option) => _buildPollOption(
                context,
                option,
                hasUserVoted,
                userVotedOption?.id == option.id,
                canVote,
              )),

          const SizedBox(height: 8),

          // Poll metadata
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: _isVoting ? Colors.blue[200] : Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (_isVoting) ...[
                      SizedBox(
                        width: 8,
                        height: 8,
                        child: CircularProgressIndicator(
                          strokeWidth: 1,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.blue[700]!),
                        ),
                      ),
                      const SizedBox(width: 4),
                    ],
                    Text(
                      '${_currentPoll.totalVotes} vote${_currentPoll.totalVotes != 1 ? 's' : ''}',
                      style: TextStyle(
                        fontSize: 10,
                        color: _isVoting ? Colors.blue[700] : Colors.grey[700],
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPollOption(
    BuildContext context,
    PollOption option,
    bool hasUserVoted,
    bool isUserChoice,
    bool canVote,
  ) {
    final percentage = option.getPercentage(_currentPoll.totalVotes);
    final isLeading = _currentPoll.totalVotes > 0 &&
        _currentPoll.options.isNotEmpty &&
        option.votes ==
            _currentPoll.options
                .map((o) => o.votes)
                .reduce((a, b) => a > b ? a : b);

    return Container(
      margin: const EdgeInsets.only(bottom: 6),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: canVote
              ? () async {
                  HapticFeedback.lightImpact();
                  setState(() {
                    _isVoting = true;
                  });

                  if (widget.onVote != null) {
                    await widget.onVote!(option.id);
                  }

                  setState(() {
                    _isVoting = false;
                  });
                }
              : null,
          borderRadius: BorderRadius.circular(10),
          splashColor: Colors.blue.withOpacity(0.1),
          highlightColor: Colors.blue.withOpacity(0.05),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: isUserChoice ? Colors.blue[600]! : Colors.grey[300]!,
                width: isUserChoice ? 2 : 1,
              ),
              color: isUserChoice ? Colors.blue[50] : Colors.white,
            ),
            child: Stack(
              children: [
                // Progress bar background (only show if votes exist)
                if (hasUserVoted && _currentPoll.totalVotes > 0)
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: FractionallySizedBox(
                        alignment: Alignment.centerLeft,
                        widthFactor: percentage / 100,
                        child: Container(
                          decoration: BoxDecoration(
                            color: isUserChoice
                                ? Colors.blue[200]!.withOpacity(0.3)
                                : Colors.grey[300]!.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(6),
                          ),
                        ),
                      ),
                    ),
                  ),

                // Option content
                Row(
                  children: [
                    // Option text
                    Expanded(
                      child: Text(
                        option.text,
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight:
                              isUserChoice ? FontWeight.w600 : FontWeight.w500,
                          color:
                              isUserChoice ? Colors.blue[800] : Colors.black87,
                        ),
                      ),
                    ),

                    // Vote count and percentage (show after voting)
                    if (hasUserVoted) ...[
                      const SizedBox(width: 12),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (isUserChoice)
                            Container(
                              padding: const EdgeInsets.all(2),
                              decoration: BoxDecoration(
                                color: Colors.blue[600],
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.check,
                                size: 12,
                                color: Colors.white,
                              ),
                            ),
                          if (isLeading &&
                              _currentPoll.totalVotes > 0 &&
                              !isUserChoice)
                            Icon(
                              Icons.trending_up,
                              size: 16,
                              color: Colors.green[600],
                            ),
                          const SizedBox(width: 6),
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: isUserChoice
                                  ? Colors.blue[600]
                                  : Colors.grey[600],
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '${percentage.toStringAsFixed(0)}%',
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],

                    // No additional vote indicator needed - entire option is clickable
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
