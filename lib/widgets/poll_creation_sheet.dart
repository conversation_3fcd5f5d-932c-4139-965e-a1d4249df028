import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/poll_model.dart';

class PollCreationSheet extends StatefulWidget {
  final Function(Poll poll) onPollCreated;

  const PollCreationSheet({
    super.key,
    required this.onPollCreated,
  });

  @override
  State<PollCreationSheet> createState() => _PollCreationSheetState();
}

class _PollCreationSheetState extends State<PollCreationSheet> {
  final _questionController = TextEditingController();
  final List<TextEditingController> _optionControllers = [
    TextEditingController(),
    TextEditingController(),
  ];
  
  bool _allowMultipleVotes = false;
  int _selectedDuration = 1; // Days
  
  static const int _maxOptions = 6;
  static const int _maxQuestionLength = 200;
  static const int _maxOptionLength = 100;

  @override
  void dispose() {
    _questionController.dispose();
    for (final controller in _optionControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                const Text(
                  'Create Poll',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _canCreatePoll() ? _createPoll : null,
                  child: Text(
                    'Create',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: _canCreatePoll() ? Colors.blue[600] : Colors.grey[400],
                    ),
                  ),
                ),
              ],
            ),
          ),

          const Divider(height: 1),

          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Question input
                  const Text(
                    'Poll Question',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: _questionController,
                    maxLength: _maxQuestionLength,
                    maxLines: 2,
                    decoration: InputDecoration(
                      hintText: 'Ask a question...',
                      hintStyle: TextStyle(color: Colors.grey[500]),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(color: Colors.grey[300]!),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(color: Colors.blue[400]!),
                      ),
                      contentPadding: const EdgeInsets.all(16),
                    ),
                    onChanged: (_) => setState(() {}),
                  ),

                  const SizedBox(height: 24),

                  // Options
                  Row(
                    children: [
                      const Text(
                        'Poll Options',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      const Spacer(),
                      if (_optionControllers.length < _maxOptions)
                        TextButton.icon(
                          onPressed: _addOption,
                          icon: const Icon(Icons.add, size: 16),
                          label: const Text('Add'),
                          style: TextButton.styleFrom(
                            foregroundColor: Colors.blue[600],
                            textStyle: const TextStyle(fontSize: 14),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Option inputs
                  ...List.generate(_optionControllers.length, (index) {
                    return Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      child: Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: _optionControllers[index],
                              maxLength: _maxOptionLength,
                              decoration: InputDecoration(
                                hintText: 'Option ${index + 1}',
                                hintStyle: TextStyle(color: Colors.grey[500]),
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: BorderSide(color: Colors.grey[300]!),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: BorderSide(color: Colors.blue[400]!),
                                ),
                                contentPadding: const EdgeInsets.all(16),
                                counterText: '',
                              ),
                              onChanged: (_) => setState(() {}),
                            ),
                          ),
                          if (_optionControllers.length > 2) ...[
                            const SizedBox(width: 8),
                            IconButton(
                              onPressed: () => _removeOption(index),
                              icon: const Icon(Icons.remove_circle_outline),
                              color: Colors.red[400],
                            ),
                          ],
                        ],
                      ),
                    );
                  }),

                  const SizedBox(height: 24),

                  // Poll settings
                  const Text(
                    'Poll Settings',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 12),

                  // Duration selector
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey[200]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Poll Duration',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 8,
                          children: [1, 3, 7, 30].map((days) {
                            final isSelected = _selectedDuration == days;
                            return ChoiceChip(
                              label: Text('${days}d'),
                              selected: isSelected,
                              onSelected: (selected) {
                                if (selected) {
                                  setState(() => _selectedDuration = days);
                                }
                              },
                              selectedColor: Colors.blue[100],
                              backgroundColor: Colors.white,
                              side: BorderSide(
                                color: isSelected ? Colors.blue[400]! : Colors.grey[300]!,
                              ),
                            );
                          }).toList(),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Multiple votes toggle
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey[200]!),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text(
                                'Allow Multiple Votes',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black87,
                                ),
                              ),
                              Text(
                                'Let people choose multiple options',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                        Switch(
                          value: _allowMultipleVotes,
                          onChanged: (value) {
                            setState(() => _allowMultipleVotes = value);
                          },
                          activeColor: Colors.blue[600],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _addOption() {
    if (_optionControllers.length < _maxOptions) {
      setState(() {
        _optionControllers.add(TextEditingController());
      });
    }
  }

  void _removeOption(int index) {
    if (_optionControllers.length > 2) {
      setState(() {
        _optionControllers[index].dispose();
        _optionControllers.removeAt(index);
      });
    }
  }

  bool _canCreatePoll() {
    if (_questionController.text.trim().isEmpty) return false;
    
    final validOptions = _optionControllers
        .where((controller) => controller.text.trim().isNotEmpty)
        .length;
    
    return validOptions >= 2;
  }

  void _createPoll() {
    if (!_canCreatePoll()) return;

    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) return;

    final options = _optionControllers
        .where((controller) => controller.text.trim().isNotEmpty)
        .map((controller) => PollOption(
              id: DateTime.now().millisecondsSinceEpoch.toString() + 
                  _optionControllers.indexOf(controller).toString(),
              text: controller.text.trim(),
            ))
        .toList();

    final poll = Poll(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      question: _questionController.text.trim(),
      options: options,
      createdAt: DateTime.now(),
      expiresAt: DateTime.now().add(Duration(days: _selectedDuration)),
      allowMultipleVotes: _allowMultipleVotes,
      createdBy: currentUser.uid,
    );

    widget.onPollCreated(poll);
    Navigator.of(context).pop();
  }
}
