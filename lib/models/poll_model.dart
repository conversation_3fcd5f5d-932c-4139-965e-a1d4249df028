import 'package:cloud_firestore/cloud_firestore.dart';

/// Model for poll options
class PollOption {
  final String id;
  final String text;
  final int votes;
  final List<String> votedBy; // User IDs who voted for this option

  const PollOption({
    required this.id,
    required this.text,
    this.votes = 0,
    this.votedBy = const [],
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'text': text,
      'votes': votes,
      'votedBy': votedBy,
    };
  }

  factory PollOption.fromMap(Map<String, dynamic> map) {
    return PollOption(
      id: map['id'] ?? '',
      text: map['text'] ?? '',
      votes: map['votes'] ?? 0,
      votedBy: List<String>.from(map['votedBy'] ?? []),
    );
  }

  PollOption copyWith({
    String? id,
    String? text,
    int? votes,
    List<String>? votedBy,
  }) {
    return PollOption(
      id: id ?? this.id,
      text: text ?? this.text,
      votes: votes ?? this.votes,
      votedBy: votedBy ?? this.votedBy,
    );
  }

  /// Get vote percentage
  double getPercentage(int totalVotes) {
    if (totalVotes == 0) return 0.0;
    return (votes / totalVotes) * 100;
  }
}

/// Model for polls
class Poll {
  final String id;
  final String question;
  final List<PollOption> options;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final bool allowMultipleVotes;
  final bool isActive;
  final String createdBy;

  const Poll({
    required this.id,
    required this.question,
    required this.options,
    required this.createdAt,
    this.expiresAt,
    this.allowMultipleVotes = false,
    this.isActive = true,
    required this.createdBy,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'question': question,
      'options': options.map((option) => option.toMap()).toList(),
      'createdAt': createdAt.millisecondsSinceEpoch,
      'expiresAt': expiresAt?.millisecondsSinceEpoch,
      'allowMultipleVotes': allowMultipleVotes,
      'isActive': isActive,
      'createdBy': createdBy,
    };
  }

  factory Poll.fromMap(Map<String, dynamic> map) {
    return Poll(
      id: map['id'] ?? '',
      question: map['question'] ?? '',
      options: (map['options'] as List<dynamic>?)
              ?.map((option) => PollOption.fromMap(option as Map<String, dynamic>))
              .toList() ??
          [],
      createdAt: map['createdAt'] is int
          ? DateTime.fromMillisecondsSinceEpoch(map['createdAt'])
          : (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      expiresAt: map['expiresAt'] != null
          ? (map['expiresAt'] is int
              ? DateTime.fromMillisecondsSinceEpoch(map['expiresAt'])
              : (map['expiresAt'] as Timestamp?)?.toDate())
          : null,
      allowMultipleVotes: map['allowMultipleVotes'] ?? false,
      isActive: map['isActive'] ?? true,
      createdBy: map['createdBy'] ?? '',
    );
  }

  Poll copyWith({
    String? id,
    String? question,
    List<PollOption>? options,
    DateTime? createdAt,
    DateTime? expiresAt,
    bool? allowMultipleVotes,
    bool? isActive,
    String? createdBy,
  }) {
    return Poll(
      id: id ?? this.id,
      question: question ?? this.question,
      options: options ?? this.options,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      allowMultipleVotes: allowMultipleVotes ?? this.allowMultipleVotes,
      isActive: isActive ?? this.isActive,
      createdBy: createdBy ?? this.createdBy,
    );
  }

  /// Get total votes across all options
  int get totalVotes => options.fold(0, (sum, option) => sum + option.votes);

  /// Check if poll has expired
  bool get hasExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  /// Check if user has voted
  bool hasUserVoted(String userId) {
    return options.any((option) => option.votedBy.contains(userId));
  }

  /// Get user's voted option (if any)
  PollOption? getUserVotedOption(String userId) {
    try {
      return options.firstWhere((option) => option.votedBy.contains(userId));
    } catch (e) {
      return null;
    }
  }

  /// Check if poll is still votable
  bool get canVote => isActive && !hasExpired;
}
